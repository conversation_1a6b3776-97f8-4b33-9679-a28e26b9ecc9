#!/usr/bin/env python3
"""
YT Cutter - Professional YouTube Video Clipping System
Downloads YouTube videos in optimal 1080p H.264 quality and creates professional clips.
Uses intelligent format selection for universal compatibility.
Automatically deletes source video after clipping to save space.
"""

import json
import subprocess
import os
from pathlib import Path
import shutil

class SimpleYTClipper:
    def __init__(self):
        self.base_dir = Path(".")
        self.clips_dir = self.base_dir / "Clips"
        self.yt_dlp_path = self.base_dir / "yt-dlp.exe"
        self.ffmpeg_path = self.base_dir / "ffmpeg.exe"
        
        # Create clips directory
        self.clips_dir.mkdir(exist_ok=True)
        
        # Check if tools exist
        if not self.yt_dlp_path.exists():
            raise FileNotFoundError(f"yt-dlp.exe not found at {self.yt_dlp_path}")
        if not self.ffmpeg_path.exists():
            raise FileNotFoundError(f"ffmpeg.exe not found at {self.ffmpeg_path}")

    def get_video_title(self, url):
        """Get video title from YouTube URL"""
        print(f"Getting video info...")
        cmd = [str(self.yt_dlp_path), "--print", "%(title)s", url]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Failed to get video info: {result.stderr}")
        return result.stdout.strip()

    def download_video(self, url):
        """Download video in highest quality available"""
        print(f"📥 Downloading video in highest quality (will create 1080p clips)...")

        # Download to temp file
        temp_file = self.base_dir / "temp_video.mp4"

        cmd = [
            str(self.yt_dlp_path),
            url,
            "-o", str(temp_file),
            "--format", "bv*[height<=1080][vcodec^=avc1][ext=mp4]+ba[ext=m4a]/bv*[height<=1080][ext=mp4]+ba[ext=m4a]/b[height<=1080]/b",  # Force H.264 codec for compatibility
            "--merge-output-format", "mp4"
        ]
        
        print("⏳ Download in progress...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Download failed: {result.stderr}")
            raise Exception(f"Download failed: {result.stderr}")
        
        if not temp_file.exists():
            raise Exception("Downloaded file not found")
            
        print(f"✅ Download complete: {temp_file.name}")
        return temp_file

    def find_existing_video_folder(self, video_title):
        """Check if a folder already exists for this video title"""
        # Look for folders that contain the video title (case-insensitive)
        for folder in self.clips_dir.iterdir():
            if folder.is_dir() and video_title.lower() in folder.name.lower():
                return folder
        return None

    def get_next_clip_number(self, video_folder):
        """Find the next available clip number in the folder"""
        if not video_folder.exists():
            return 1

        # Find all existing clip files
        existing_clips = list(video_folder.glob("Clip *.mp4"))
        if not existing_clips:
            return 1

        # Extract clip numbers
        clip_numbers = []
        for clip in existing_clips:
            try:
                # Extract number from "Clip X - name.mp4"
                name_parts = clip.name.split(" - ")
                if len(name_parts) >= 2:
                    num_str = name_parts[0].replace("Clip ", "")
                    clip_numbers.append(int(num_str))
            except (ValueError, IndexError):
                continue

        return max(clip_numbers) + 1 if clip_numbers else 1

    def create_clips(self, video_file, video_title, clips_config):
        """Create all clips from the video with smart folder detection"""
        # Check if folder already exists for this video
        existing_folder = self.find_existing_video_folder(video_title)

        if existing_folder:
            video_folder = existing_folder
            start_clip_number = self.get_next_clip_number(video_folder)
            print(f"📁 Found existing folder: {video_folder.name}")
            print(f"🔄 Adding new clips starting from Clip {start_clip_number}")
        else:
            # Create new video folder
            video_folder = self.clips_dir / f"1. {video_title}"
            video_folder.mkdir(exist_ok=True)
            start_clip_number = 1
            print(f"📁 Created new folder: {video_folder.name}")

        print(f"🎬 Creating {len(clips_config)} clips...")

        for i, clip in enumerate(clips_config):
            clip_number = start_clip_number + i
            clip_name = clip['name']
            start_time = clip['start_time']
            duration = clip['duration']

            # Clean filename
            safe_name = "".join(c for c in clip_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            clip_filename = f"Clip {clip_number} - {safe_name}.mp4"
            clip_path = video_folder / clip_filename

            print(f"✂️  Creating clip {clip_number}: {clip_name}")

            # Use ffmpeg to create clip with proper keyframe seeking
            cmd = [
                str(self.ffmpeg_path),
                "-ss", str(start_time),   # Seek before input for faster processing
                "-i", str(video_file),
                "-t", str(duration),
                "-c:v", "libx264",        # Re-encode video to fix keyframe issues
                "-c:a", "aac",            # Re-encode audio for compatibility
                "-crf", "18",             # High quality (lower = better quality)
                "-preset", "fast",        # Fast encoding preset
                "-avoid_negative_ts", "make_zero",
                str(clip_path),
                "-y"  # Overwrite if exists
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"❌ Failed to create clip {clip_number}: {result.stderr}")
            else:
                print(f"✅ Clip {clip_number} created: {clip_filename}")

        return video_folder

    def cleanup(self, temp_video_file, video_folder):
        """Delete the temporary video file and Source folder"""
        try:
            # Delete temp video file
            if temp_video_file.exists():
                temp_video_file.unlink()
                print(f"🗑️  Deleted source video: {temp_video_file.name}")

            # Delete Source folder if it exists
            source_folder = video_folder / "Source"
            if source_folder.exists():
                shutil.rmtree(source_folder)
                print(f"🗑️  Deleted Source folder")

        except Exception as e:
            print(f"⚠️  Could not delete files: {e}")

    def process_video(self, config_file="clips_config.json"):
        """Main processing function"""
        # Load config
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        url = config['url']
        clips = config['clips']
        
        print("=== Simple YouTube Clipper ===")
        print(f"URL: {url}")
        print(f"Number of clips: {len(clips)}")
        print()
        
        try:
            # Get video title
            video_title = self.get_video_title(url)
            print(f"Video: {video_title}")
            print()
            
            # Download video
            temp_video = self.download_video(url)
            
            # Create clips
            video_folder = self.create_clips(temp_video, video_title, clips)
            
            # Cleanup
            self.cleanup(temp_video, video_folder)
            
            print()
            print(f"🎉 All done! Clips saved to: {video_folder}")
            print(f"📊 Created {len(clips)} clips successfully")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        
        return True

def main():
    clipper = SimpleYTClipper()
    clipper.process_video()

if __name__ == "__main__":
    main()
