# 🎬 YT Cutter - Professional YouTube Video Clipping System

**Transform any YouTube video into high-quality clips with precision timestamps**

A production-ready tool that downloads YouTube videos in optimal quality and creates professional 1080p H.264 clips with universal compatibility. Perfect for content creators, social media automation, and video editing workflows.

---

## 🌟 **Key Features**

### 🎯 **Professional Quality Output**
- **True 1080p Resolution** (1920x1080) with 60fps support
- **H.264 Codec** for universal compatibility (plays everywhere)
- **High Bitrate** (~4000k) for crisp, professional quality
- **MP4 Format** optimized for all platforms and devices

### ⚡ **Smart Download Technology**
- **Intelligent Format Selection**: Automatically chooses best quality H.264 source
- **Fallback System**: Gracefully handles quality variations across videos
- **Efficient Processing**: Downloads highest quality, re-encodes for perfect keyframes
- **Space Management**: Auto-deletes source files after clipping

### 📁 **Organized Output Structure**
- **Smart Folder Detection**: Automatically detects existing video folders
- **Sequential Numbering**: Clips numbered 1, 2, 3... for easy organization
- **No Duplication**: Never creates multiple folders for the same video
- **Sanitized Filenames**: Cross-platform compatible naming
- **Batch Processing**: Handle multiple clips from single video

---

## 🚀 **Quick Start Guide**

### **1. Configure Your Clips**
Edit `clips_config.json` with your video URL and desired clips:

```json
{
  "url": "https://www.youtube.com/watch?v=rSDI53eMJJI",
  "clips": [
    {
      "name": "Opening Scene",
      "start_time": 15,
      "duration": 30
    },
    {
      "name": "Best Moment",
      "start_time": 120,
      "duration": 45
    },
    {
      "name": "Epic Conclusion",
      "start_time": 300,
      "duration": 25
    }
  ]
}
```

### **2. Run the System**
```bash
python yt_clipper_simple.py
```

### **3. Get Your Clips**
Find your professional-quality clips in:
```
Clips/
└── Video Title Name/
    ├── Clip 1 - Opening Scene.mp4
    ├── Clip 2 - Best Moment.mp4
    └── Clip 3 - Epic Conclusion.mp4
```

### **4. Add More Clips (Same Video)**
Update `clips_config.json` with new clips and run again:
- ✅ **Smart Detection**: Finds existing video folder automatically
- ✅ **Sequential Numbering**: New clips continue from last number (Clip 4, 5, 6...)
- ✅ **No Duplication**: Never creates duplicate folders

---

## 📋 **System Requirements**

### **Included Tools** (No Installation Required)
- ✅ **yt-dlp.exe** - Latest YouTube downloader
- ✅ **ffmpeg.exe** - Professional video processing
- ✅ **Python Script** - Automated workflow management

### **System Compatibility**
- ✅ **Windows** (Primary support)
- ✅ **Cross-platform** file naming
- ✅ **Universal output** (plays on all devices)

---

## 🎛️ **Configuration Reference**

### **clips_config.json Structure**
```json
{
  "url": "YouTube video URL",
  "clips": [
    {
      "name": "Descriptive clip name",
      "start_time": 60,    // Start time in seconds
      "duration": 30       // Clip length in seconds
    }
  ]
}
```

### **Timing Guidelines**
- **start_time**: Exact second to begin clip (supports decimals: 15.5)
- **duration**: Length of clip in seconds (supports decimals: 30.25)
- **Precision**: Frame-accurate cutting with ffmpeg
- **Validation**: System checks for valid timestamps

---

## 🔧 **Technical Specifications**

### **Download Quality Optimization**
```
Format Selection: "bv*[height<=1080][vcodec^=avc1][ext=mp4]+ba[ext=m4a]/bv*[height<=1080][ext=mp4]+ba[ext=m4a]/b[height<=1080]/b"
```

**Priority Chain:**
1. **1080p H.264 MP4** + **AAC Audio** (Preferred)
2. **1080p Any Codec MP4** + **AAC Audio** (Fallback)
3. **Best Available ≤1080p** (Final fallback)

### **Clipping Process**
- **Method**: High-quality re-encoding with keyframe optimization
- **Quality**: CRF 18 encoding for near-lossless quality
- **Speed**: Fast preset for efficient processing
- **Accuracy**: Frame-perfect timestamp cutting with instant playback

### **Output Specifications**
- **Video Codec**: H.264 (AVC1) - Universal compatibility
- **Audio Codec**: AAC - High quality, small size
- **Container**: MP4 - Maximum platform support
- **Resolution**: Up to 1920x1080 (1080p)
- **Frame Rate**: Preserves source (typically 30fps or 60fps)
- **Bitrate**: ~4000k video, ~128k audio

---

## 📁 **Project Structure**

```
YTcutter/
├── 📄 yt_clipper_simple.py     # Main processing script
├── ⚙️ clips_config.json        # Configuration file
├── 🛠️ ffmpeg.exe              # Video processing tool
├── 📥 yt-dlp.exe               # YouTube downloader
├── 📖 README.md                # This documentation
└── 📁 Clips/                   # Output directory
    └── 📁 [Video Title]/       # Auto-created per video
        ├── 🎬 Clip 1 - [Name].mp4
        ├── 🎬 Clip 2 - [Name].mp4
        └── 🎬 Clip 3 - [Name].mp4
```

---

## 💡 **Pro Tips & Best Practices**

### **Quality Optimization**
- ✅ **Always downloads highest available quality** (up to 1080p)
- ✅ **Forces H.264 codec** for maximum compatibility
- ✅ **CRF 18 encoding** for near-lossless quality
- ✅ **Perfect keyframe alignment** for instant playback

### **Workflow Efficiency**
- 🎯 **Preview timestamps** in YouTube before configuring
- 🎯 **Use descriptive names** for easy clip identification
- 🎯 **Batch multiple clips** from same video efficiently
- 🎯 **Check video duration** to ensure valid timestamps

### **File Management**
- 🗂️ **Auto-organized** by video title
- 🗂️ **Sequential numbering** for logical ordering
- 🗂️ **Source cleanup** saves disk space automatically
- 🗂️ **Cross-platform naming** ensures compatibility

---

## 🎬 **Use Cases**

### **Content Creation**
- **Social Media Clips**: Extract viral moments for TikTok, Instagram, Twitter
- **Highlight Reels**: Create best-of compilations from long-form content
- **Tutorial Segments**: Break down educational content into digestible clips

### **Business Applications**
- **Marketing Content**: Extract key moments for promotional materials
- **Training Materials**: Create focused learning segments
- **Content Repurposing**: Transform long videos into multiple short-form pieces

### **Automation Integration**
- **Batch Processing**: Handle multiple videos with different configurations
- **Workflow Integration**: Embed in larger content creation pipelines
- **Quality Assurance**: Consistent output for professional applications

---

## 🛡️ **Quality Guarantees**

### **Universal Compatibility**
- ✅ **All Video Players**: VLC, Windows Media Player, QuickTime
- ✅ **All Browsers**: Chrome, Firefox, Safari, Edge
- ✅ **All Devices**: Desktop, mobile, tablets
- ✅ **All Platforms**: YouTube, TikTok, Instagram, Twitter

### **Professional Standards**
- ✅ **Broadcast Quality**: Suitable for professional use
- ✅ **High Bitrate**: Crisp, clear visuals
- ✅ **Smooth Playback**: Optimized encoding settings
- ✅ **No Artifacts**: Clean, professional output

---

## 📝 **Legal & Usage Notes**

- **Copyright Compliance**: Always respect copyright and obtain proper permissions
- **Fair Use**: Ensure your usage complies with fair use guidelines
- **Platform Terms**: Respect YouTube's Terms of Service
- **Content Rights**: Only process content you have rights to use

---

## 🔄 **Version History**

**Current Version**: Production-Ready Smart Clipping System
- ✅ **1080p H.264 Output**: Universal compatibility achieved
- ✅ **Smart Folder Detection**: Prevents duplicate folders, sequential numbering
- ✅ **Keyframe Optimization**: Instant playback, no frozen frames
- ✅ **CRF 18 Encoding**: Near-lossless quality with perfect compatibility
- ✅ **Automated Cleanup**: Efficient space management
- ✅ **Production Ready**: Stable, reliable operation

---

*Built for content creators who demand professional quality and universal compatibility. Transform any YouTube video into premium clips with precision and efficiency.*
